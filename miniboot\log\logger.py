#!/usr/bin/env python
"""
* @author: cz
* @description: Mini-Boot 日志系统模块

基于 loguru 实现的简洁日志系统, 采用Logger类封装设计.
提供了一个Logger类来管理日志配置和初始化, 同时保持loguru的简洁使用方式.

主要特性:
- Logger类封装: 使用Logger类管理日志配置和初始化
- 全局日志对象: 配置完成后, 所有模块共享同一个loguru日志实例
- 配置驱动: 通过环境配置模块配置日志系统
- 简洁易用: 配置后其他模块只需 `from loguru import logger` 即可使用
- 环境集成: 与环境配置模块深度集成, 支持多环境配置
- 配置优先级: 支持命令行参数, 环境变量, 配置文件等多种配置源

使用示例:
    # 1. 应用启动时配置日志
    from miniboot.env import StandardEnvironment
    from miniboot.log import Logger

    env = StandardEnvironment()
    logger_manager = Logger()
    logger_manager.configure(env)

    # 2. 在任何模块中使用日志
    from loguru import logger
    logger.info("这是一条日志")
"""

import sys
from pathlib import Path
from typing import Any, Optional, TYPE_CHECKING

from loguru import logger

from .properties import ConsoleConfig, FileConfig, LoggingProperties

if TYPE_CHECKING:
    from miniboot.env.environment import Environment


class Logger:
    """Mini-Boot 日志管理器

    负责管理和配置整个应用的日志系统. 基于loguru实现, 提供简洁的配置接口.
    配置完成后, 所有模块都可以直接使用 `from loguru import logger` 来记录日志.

    Attributes:
        _configured (bool): 是否已配置标志
        _environment: 环境配置对象
    """

    def __init__(self, properties: Optional[LoggingProperties] = None):
        """初始化日志管理器

        Args:
            properties: 日志配置属性对象(可选)
        """
        self._configured = False
        self._properties = properties

    def configure(self, environment: Optional["Environment"] = None, properties: Optional[LoggingProperties] = None) -> None:
        """配置全局日志对象

        Args:
            environment: 环境配置对象
            properties: 日志配置属性对象(优先级高于environment)

        Example:
            >>> from miniboot.env import StandardEnvironment
            >>> from miniboot.log import Logger, LoggingProperties
            >>> env = StandardEnvironment()
            >>> # 方式1: 使用环境配置
            >>> logger_manager = Logger()
            >>> logger_manager.configure(env)
            >>> # 方式2: 使用配置属性对象
            >>> props = LoggingProperties.from_environment(env)
            >>> logger_manager = Logger(props)
            >>> logger_manager.configure()
        """
        if self._configured:
            logger.warning("日志系统已经配置过, 跳过重复配置")
            return

        # 移除 loguru 的默认处理器
        logger.remove()

        # 获取日志配置
        if properties:
            self._properties = properties
        elif self._properties is None:
            if environment:
                self._properties = LoggingProperties.from_environment(environment)
            else:
                self._properties = LoggingProperties()

        # 验证配置
        self._properties.validate()

        # 配置控制台输出
        if self._properties.console.enabled:
            self._configure_console(self._properties.console)

        # 配置文件输出
        if self._properties.file.enabled:
            self._configure_file(self._properties.file)

        self._configured = True
        logger.info("日志系统初始化完成")

    def _configure_console(self, console_config: ConsoleConfig) -> None:
        """配置控制台输出

        Args:
            console_config: 控制台配置对象
        """
        logger.add(
            sys.stderr,
            level=console_config.level,
            format=console_config.format,
            colorize=console_config.colorize,
            backtrace=True,
            diagnose=True,
        )

    def _configure_file(self, file_config: FileConfig) -> None:
        """配置文件输出

        Args:
            file_config: 文件配置对象
        """
        log_path = file_config.path

        # 确保日志目录存在
        log_dir = Path(log_path).parent
        log_dir.mkdir(parents=True, exist_ok=True)

        # 处理压缩配置
        compression = file_config.compression
        if compression and compression.lower() in ["none", "false", ""]:
            compression = None

        logger.add(
            log_path,
            level=file_config.level,
            rotation=file_config.rotation,
            retention=file_config.retention,
            compression=compression,
            format=file_config.format,
            encoding="utf-8",
            backtrace=True,
            diagnose=True,
        )

    def update_log_level(self, level: str) -> None:
        """动态更新日志级别

        注意:loguru 不支持动态更新级别,这个方法主要用于记录级别变更.
        如果需要真正的动态更新,需要重新调用 configure 方法.

        Args:
            level: 新的日志级别
        """
        logger.info(f"日志级别更新为: {level}")

    def is_configured(self) -> bool:
        """检查日志系统是否已配置

        Returns:
            bool: 是否已配置
        """
        return self._configured

    def get_properties(self) -> Optional[LoggingProperties]:
        """获取当前使用的日志配置属性对象

        Returns:
            LoggingProperties: 日志配置属性对象
        """
        return self._properties
